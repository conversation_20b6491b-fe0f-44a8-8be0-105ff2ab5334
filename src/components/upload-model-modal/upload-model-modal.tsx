import React, { useRef, useState } from 'react';
import './upload-model-modal.css';
import { UploadCloud } from 'lucide-react';
import { createDragUploadHandlers, handleFileInputChange, FILE_VALIDATION_CONFIGS } from '../../utils/fileUpload';
import { Modal } from '../modal/modal';

interface UploadModelModalProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
}

export const UploadModelModal: React.FC<UploadModelModalProps> = ({ visible, onClose, onUpload }) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);


  if (!visible) return null;

  const handleFileSelect = (file: File) => {
    onUpload(file);
  };

  const dragHandlers = createDragUploadHandlers(
    handleFileSelect,
    setDragOver,
    FILE_VALIDATION_CONFIGS.MODEL
  );

  return (
    <Modal
      visible={visible}
      title="上传模型"
      onClose={onClose}
      size="medium"
    >
      <div
        className={`upload-area ${dragOver ? 'drag-over' : ''}`}
        onDrop={dragHandlers.onDrop}
        onDragOver={dragHandlers.onDragOver}
        onDragLeave={dragHandlers.onDragLeave}
        onClick={() => inputRef.current?.click()}
      >
        <UploadCloud size={48} />
        <p>点击选择模型文件，或拖拽到此处上传</p>
        <p style={{ color: 'var(--color-content-mute)' }}>支持格式：glb, gltf</p>

      </div>

      <input
        ref={inputRef}
        type="file"
        accept=".glb,.gltf"
        style={{ display: 'none' }}
        onChange={(e) => handleFileInputChange(e, handleFileSelect, FILE_VALIDATION_CONFIGS.MODEL)}
      />


    </Modal>
  );
};
