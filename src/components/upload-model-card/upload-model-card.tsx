import React, { useRef } from 'react';
import { Upload } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import './upload-model-card.css';

interface UploadModelCardProps {
  className?: string;
}

export const UploadModelCard: React.FC<UploadModelCardProps> = ({ className = '' }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleCardClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 仅允许 GLB/GLTF
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (!ext || !['glb', 'gltf'].includes(ext)) {
      alert('请上传 GLB 或 GLTF 格式的文件');
      return;
    }

    // 将文件传递到渲染页面（使用路由 state）
    navigate('/render', {
      state: { uploadedFile: file }
    });
  };

  return (
    <div className={`model-card upload-model-card ${className}`} onClick={handleCardClick}>
      <div className="upload-model-card__icon">
        <Upload size={48} />
      </div>
      <h3 className="model-card__title">上传模型</h3>
      {/* 隐藏的文件选择 */}
      <input
        type="file"
        ref={fileInputRef}
        accept=".glb,.gltf"
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
    </div>
  );
};
