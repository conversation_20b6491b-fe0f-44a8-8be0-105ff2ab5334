import { useState, useEffect, useRef, useCallback } from 'react';
import { SearchBox } from '../../components/search-box/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { Loading } from '../../components/loading/loading';
import { UploadCloud, Plus, Trash, Edit, X } from 'lucide-react';
import './AdminTable.css';
import '../../components/modal/modal.css';

import { apiService } from '../../services/api';
import type { ModelData } from '../../services/api';

// 使用从API服务导入的ModelData接口

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ModelManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ModelData | null>(null);
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<ModelData[]>([]);

  // New state for uploads
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadMessage, setUploadMessage] = useState('');

  // Load models list
  useEffect(() => {
    const loadModels = async () => {
      setLoading(true);
      try {
        const data = await apiService.getModels();
        setModels(data);
      } catch (error) {
        console.error('Failed to load models:', error);
      } finally {
        setLoading(false);
      }
    };
    loadModels();
  }, []);

  const deleteModel = async (id: string) => {
    if (window.confirm('确定要删除这个模型吗？')) {
      try {
        await apiService.deleteModel(id);
        setModels(models.filter(model => model.id !== id));
      } catch (error) {
        console.error('Failed to delete model:', error);
      }
    }
  };

    const handleSaveModel = useCallback(async (name: string, modelFile: File | null, thumbnailFile: File | null) => {
    if (!modelFile) {
      alert('模型文件不能为空！');
      return;
    }

    setUploading(true);
    try {
      setUploadMessage('正在上传模型文件...');
      setUploadProgress(0);
      const modelUrl = await apiService.uploadFile(modelFile, setUploadProgress);

      let thumbnailUrl: string | undefined = undefined;
      if (thumbnailFile) {
        setUploadMessage('正在上传缩略图...');
        setUploadProgress(0);
        thumbnailUrl = await apiService.uploadFile(thumbnailFile, setUploadProgress);
      }

      setUploadMessage('正在创建模型记录...');
      const newModelData = await apiService.addModel({
        name,
        fileType: modelFile.name.split('.').pop()?.toUpperCase() || 'GLB',
        size: modelFile.size.toString(),
        url: modelUrl,
        thumbnailUrl,
      });

      if (newModelData) {
        const finalModelData = { ...newModelData, size: modelFile.size };
        setModels(currentModels => [...currentModels, finalModelData]);
      } else {
        throw new Error('API did not return new model data.');
      }

      setShowAddModal(false);
    } catch (error) {
      console.error('Failed to add model:', error);
      alert(`添加模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setUploading(false);
      setUploadMessage('');
      setUploadProgress(0);
    }
  }, []);
  
    const handleUpdateModel = useCallback(async (name: string, _modelFile: File | null, thumbnailFile: File | null, existingModel?: ModelData) => {
    if (!existingModel) return;

    setUploading(true);
    try {
      let thumbnailUrl: string | undefined | null = existingModel.thumbnail;

      if (thumbnailFile) {
        setUploadMessage('正在上传新缩略图...');
        setUploadProgress(0);
        thumbnailUrl = await apiService.uploadFile(thumbnailFile, setUploadProgress);
      }

      setUploadMessage('正在更新模型信息...');
      const updatedModel = await apiService.updateModel(existingModel.id, name, thumbnailUrl);

      if (updatedModel) {
        setModels(currentModels => 
          currentModels.map(m => m.id === updatedModel.id ? { ...updatedModel, size: m.size } : m)
        );
      } else {
        throw new Error('API did not return updated model data.');
      }

      setSelectedModel(null);
    } catch (error) {
      console.error('Failed to update model:', error);
      alert(`更新模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setUploading(false);
      setUploadMessage('');
      setUploadProgress(0);
    }
  }, []);

  const filteredModels = models.filter(model => 
    model.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return <Loading text="正在加载模型列表..." variant="minimal" />;
  }

  return (
    <div className="model-management">
      <div className="management-toolbar">
        <SearchBox 
          placeholder="搜索模型" 
          value={searchQuery} 
          onChange={setSearchQuery}
          width={300}
        />
        
        <div className="toolbar-actions">
          <PrimaryButton icon={Plus} onClick={() => setShowAddModal(true)}>
            添加模型
          </PrimaryButton>
        </div>
      </div>
      
      <div className="admin-table-container">
        <table className="admin-table">
          <thead>
            <tr>
              <th>缩略图</th>
              <th>模型名称</th>
              <th>文件类型</th>
              <th>文件大小</th>
              <th>上传时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredModels.map(model => (
              <tr key={model.id} className="model-row">
                <td className="thumbnail-cell">
                  {model.thumbnail ? (
                    <img 
                      src={model.thumbnail} 
                      alt={model.name}
                      className="thumbnail-image"
                      onError={(e) => {
                        // 图片加载失败时隐藏图片，显示占位符
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const placeholder = target.parentElement?.querySelector('.thumbnail-placeholder');
                        if (placeholder) {
                          (placeholder as HTMLElement).style.display = 'flex';
                        }
                      }}
                    />
                  ) : null}
                  <div className="thumbnail-placeholder" style={{ display: model.thumbnail ? 'none' : 'flex' }}>
                    <UploadCloud size={20} />
                  </div>
                </td>
                <td className="model-name">{model.name}</td>
                <td className="file-type"><span className="file-type-badge">{model.fileType}</span></td>
                <td className="file-size">
                  {(() => {
                    const sizeInBytes = typeof model.size === 'number' ? model.size : Number(model.size);
                    if (!sizeInBytes || isNaN(sizeInBytes)) return 'N/A';
                    return `${(sizeInBytes / 1024 / 1024).toFixed(2)} MB`;
                  })()}
                </td>
                <td className="date-cell">{formatDate(model.createdAt)}</td>
                <td className="actions-cell">
                  <IconButton icon={Edit} size="small" onClick={() => setSelectedModel(model)} />
                  <IconButton icon={Trash} size="small" onClick={() => deleteModel(model.id)} />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showAddModal && (
        <ModelModal 
          onClose={() => setShowAddModal(false)} 
          onSave={handleSaveModel}
          uploading={uploading}
          uploadProgress={uploadProgress}
          uploadMessage={uploadMessage}
        />
      )}
      
      {selectedModel && (
        <ModelModal 
          model={selectedModel}
          onClose={() => setSelectedModel(null)} 
          onSave={handleUpdateModel}
          uploading={uploading}
          uploadProgress={uploadProgress}
          uploadMessage={uploadMessage}
        />
      )}
    </div>
  );
};

interface ModelModalProps {
  model?: ModelData;
  onClose: () => void;
  onSave: (name: string, modelFile: File | null, thumbnailFile: File | null, existingModelData?: ModelData) => void | Promise<void>;
  uploading: boolean;
  uploadProgress: number;
  uploadMessage: string;
}

// 模型添加/编辑弹窗
const ModelModal = ({ 
  model, 
  onClose, 
  onSave,
  uploading,
  uploadProgress,
  uploadMessage 
}: ModelModalProps) => {
  const [name, setName] = useState(model?.name || '');
  const [modelFile, setModelFile] = useState<File | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const [thumbnailDragOver, setThumbnailDragOver] = useState(false);

  const handleModelFile = (file?: File) => {
    if (!file) return;
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (ext !== 'glb') {
      alert('只支持 .glb 格式模型文件');
      return;
    }
    setModelFile(file);
    const baseName = file.name.replace(/\.[^/.]+$/, '');
    setName(baseName);
  };

  const handleDrop: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    const file = e.dataTransfer.files?.[0];
    handleModelFile(file);
  };

  const handleDragOver: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dragOver) setDragOver(true);
  };

  const handleDragLeave: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };
  
    const handleThumbnailFile = (file?: File) => {
    if (file && file.type.startsWith('image/')) {
      setThumbnailFile(file);
    } else if (file) {
      alert('请上传图片文件 (png, jpg, webp)');
    }
  };

  const handleThumbnailDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setThumbnailDragOver(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleThumbnailFile(e.dataTransfer.files[0]);
    }
  };

  const handleThumbnailDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setThumbnailDragOver(true);
  };

  const handleThumbnailDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setThumbnailDragOver(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!model && !modelFile) {
      alert('请选择一个模型文件。');
      return;
    }

    onSave(name, modelFile, thumbnailFile, model);
  };
  
  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <h2 className="modal-title">{model ? '编辑模型' : '添加新模型'}</h2>
          <IconButton icon={X} onClick={onClose} disabled={uploading} aria-label="关闭" />
        </div>
        <div className="modal-content">
          <form className="modal-form" onSubmit={handleSubmit}>
            <fieldset disabled={uploading} className="form-fieldset">
              {/* 上传模型文件 */}
              <div className="form-group">
                <label>上传模型文件 (.glb)</label>
                <div
                  className={`upload-area ${dragOver ? 'drag-over' : ''}`}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <UploadCloud size={48} />
                  <p>点击选择模型文件，或拖拽到此处上传</p>
                  {modelFile && <p className="file-name">已选择: {modelFile.name}</p>}
                  {model && !modelFile && <p className="file-name">当前模型: {model.name}.glb</p>}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".glb"
                  style={{ display: 'none' }}
                  onChange={(e) => handleModelFile(e.target.files?.[0])}
                />
              </div>

              {/* 模型名称 */}
              <div className="form-group">
                <label>模型名称</label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="输入模型名称"
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>上传缩略图 (可选)</label>
                <div
                  className={`upload-area ${thumbnailDragOver ? 'drag-over' : ''}`}
                  onDrop={handleThumbnailDrop}
                  onDragOver={handleThumbnailDragOver}
                  onDragLeave={handleThumbnailDragLeave}
                  onClick={() => document.getElementById('thumbnail-file-input')?.click()}
                >
                  <UploadCloud size={48} />
                  <p>点击选择图片，或拖拽到此处上传</p>
                  {thumbnailFile && <p className="file-name">已选择: {thumbnailFile.name}</p>}
                  {model && !thumbnailFile && model.thumbnail && <p className="file-name">当前缩略图: <img src={model.thumbnail} alt="thumbnail" style={{width: '30px', verticalAlign: 'middle', marginLeft: '10px'}} /></p>}
                </div>
                <input 
                  type="file" 
                  id="thumbnail-file-input"
                  onChange={(e) => handleThumbnailFile(e.target.files?.[0])} 
                  accept="image/png, image/jpeg, image/webp"
                  style={{ display: 'none' }}
                />
              </div>
            </fieldset>

            {uploading && (
              <div className="upload-status">
                <p className="upload-message">{uploadMessage}</p>
                <div className="progress-bar-container">
                  <div className="progress-bar" style={{ width: `${uploadProgress}%` }}></div>
                </div>
                <span className="upload-percentage">{`${Math.round(uploadProgress)}%`}</span>
              </div>
            )}
            
            <div className="modal-actions">
              <PrimaryButton type="submit" disabled={uploading} showIcon={false}>
                {uploading ? '上传中...' : '保存'}
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ModelManagement;