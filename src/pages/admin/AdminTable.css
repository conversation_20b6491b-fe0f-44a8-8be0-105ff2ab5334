/* Shared styles for admin management tables */

.management-toolbar{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.admin-table-container {
  overflow-x: auto;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-base);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
}

.admin-table tbody tr {
  height: 80px;
}

.admin-table td {
  height: 100%;
}

.admin-table th,
.admin-table td {
  padding: var(--spacing-base) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.admin-table th {
  font-weight: 500;
  white-space: nowrap;
}

.admin-table tbody tr:hover {
  background-color: var(--color-bg-hover);
}

/* Common column styles */
.admin-table .thumbnail-cell {
  width: 80px;
  min-width: 60px;
  max-width: 100px;
}

.admin-table .thumbnail-image {
  max-width: 80px;
  max-height: 60px;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
}

.admin-table .thumbnail-placeholder {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-sm);
}

.admin-table .date-cell {
  white-space: nowrap;
  min-width: 100px;
}

.admin-table .actions-cell {
  width: 120px;
  text-align: left;
}

.admin-table .actions-cell > .icon-button + .icon-button {
  margin-left: 8px;
}

/* Modal Form Styles */
.modal-form .form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.model-upload-group {
  flex: 1;
}

.thumbnail-upload-group {
  display: flex;
  flex-direction: column;
}

.thumbnail-uploader {
  width: 100px;
  height: 100px;
  border: 2px dashed var(--color-border);
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: var(--color-bg-overlay);
  overflow: hidden;
}

.thumbnail-uploader:hover,
.thumbnail-uploader.drag-over {
  border-color: var(--color-primary);
}

.thumbnail-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder-icon {
  color: var(--color-content-secondary);
}