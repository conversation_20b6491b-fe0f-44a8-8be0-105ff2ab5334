import { useState, useRef, useEffect, Suspense, useCallback, useMemo } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useNotification } from '../components/notification/notification';
import { validateFile, FILE_VALIDATION_CONFIGS } from '../utils/fileUpload';
import { StatusMessage } from '../components/status-message/status-message';
import { EmptyState } from '../components/empty-state/empty-state';
import './RenderPage.css';
import { SearchBox } from '../components/search-box/search-box';
import { PrimaryButton } from '../components/primary-button/primary-button';
import { SecondaryButton } from '../components/secondary-button/secondary-button';
import { DropDown } from '../components/drop-down/drop-down';
import { TabGroup } from '../components/tab-group/tab-group';
import { TabItem } from '../components/tab-item/tab-item';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import { ErrorBoundary } from '../components/error-boundary/error-boundary';
import { Loading } from '../components/loading/loading';
import { Tooltip } from '../components/tooltip/tooltip';
import { Copy, Download, Upload, HelpCircle, RotateCcw, Box, Paintbrush, Trash2 } from 'lucide-react';
import LogoImage from '../assets/images/Logo.png';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF, Stage, Html } from '@react-three/drei';
import type { OrbitControls as OrbitControlsImpl } from 'three-stdlib';
import * as THREE from 'three';

// 创建全局纹理加载器和缓存
const textureLoader = new THREE.TextureLoader();
const textureCache = new Map<string, THREE.Texture>();

// 纹理加载函数，带缓存机制和错误处理
const loadTexture = (url: string): THREE.Texture | null => {
  if (!url) return null;

  if (textureCache.has(url)) {
    return textureCache.get(url)!;
  }

  try {
    const texture = textureLoader.load(
      url,
      // onLoad
      () => {
        console.log(`Texture loaded successfully: ${url}`);
      },
      // onProgress
      undefined,
      // onError
      (error) => {
        console.error(`Failed to load texture: ${url}`, error);
        textureCache.delete(url); // 从缓存中移除失败的纹理
      }
    );

    // 设置纹理参数以提高性能
    texture.generateMipmaps = true;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;

    textureCache.set(url, texture);
    return texture;
  } catch (error) {
    console.error(`Error creating texture loader for: ${url}`, error);
    return null;
  }
};

// 清理纹理缓存
const clearTextureCache = () => {
  textureCache.forEach((texture) => {
    texture.dispose();
  });
  textureCache.clear();
};
import MaterialThumbnail from '../components/material-thumbnail/material-thumbnail';

import { UploadModelModal } from '../components/upload-model-modal/upload-model-modal';

import { apiService } from '../services/api';
import type { ModelData, MaterialData } from '../services/api';

interface MaterialProps {
  textureUrl?: string;
  color: string;
  metalness: number; // 0-1
  roughness: number; // 0-1
  opacity: number;   // 0-1, 1 为不透明
}

// 每个 Mesh 名称 -> 对应材质
type PerMeshMaterials = Record<string, MaterialProps>;

// 原始材质信息
interface OriginalMaterial {
  name: string;
  meshName: string;
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  isEditable: boolean; // 是否以Editable开头
}

interface ModelProps {
  modelPath: string;
  perMeshMaterials: PerMeshMaterials;
  activeMaterialName: string | null; // 当前激活的材质名称，用于高亮
  onOriginalMaterialsExtracted: (materials: OriginalMaterial[]) => void;
}

const Model = ({ modelPath, perMeshMaterials, activeMaterialName, onOriginalMaterialsExtracted }: ModelProps) => {
  const { scene } = useGLTF(modelPath);
  const [materialsExtracted, setMaterialsExtracted] = useState(false);
  const materialsExtractedRef = useRef(false);
  const createdMaterialsRef = useRef<THREE.Material[]>([]);
  const originalMaterialsRef = useRef<Map<string, THREE.Material>>(new Map());
  


  // 当模型路径变化时重置材质提取状态
  useEffect(() => {
    setMaterialsExtracted(false);
    materialsExtractedRef.current = false;

    // 清理之前创建的材质
    createdMaterialsRef.current.forEach(material => {
      material.dispose();
    });
    createdMaterialsRef.current = [];

    // 清理原始材质缓存
    originalMaterialsRef.current.clear();
  }, [modelPath]);

  // 组件卸载时清理材质和纹理
  useEffect(() => {
    return () => {
      createdMaterialsRef.current.forEach(material => {
        material.dispose();
      });
      // 清理纹理缓存（仅在组件完全卸载时）
      clearTextureCache();
    };
  }, []);
  


  useEffect(() => {
    // ---------- 统一模型归一化：重置、缩放、居中 ----------
    // ① 重置
    scene.position.set(0, 0, 0);
    scene.rotation.set(0, 0, 0);
    scene.scale.set(1, 1, 1);

    // ② 计算包围盒尺寸
    const box = new THREE.Box3().setFromObject(scene);
    const size = new THREE.Vector3();
    box.getSize(size);
    const maxDim = Math.max(size.x, size.y, size.z);

    // ③ 计算缩放系数，让最长边 = TARGET_SIZE
    const TARGET_SIZE = 2;  // 统一的目标长度
    const scale = TARGET_SIZE / maxDim;
    scene.scale.setScalar(scale);

    // ④ 重新计算包围盒并居中
    box.setFromObject(scene);
    const center = new THREE.Vector3();
    box.getCenter(center);
    scene.position.set(-center.x, -center.y, -center.z);

    // 只在首次加载时提取原始材质信息，使用ref防止重复提取
    if (!materialsExtracted && !materialsExtractedRef.current) {
      materialsExtractedRef.current = true;
      const originalMaterials: OriginalMaterial[] = [];
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          const material = object.material as THREE.MeshStandardMaterial;
          const materialName = material.name || `Material_${object.name}`;

          originalMaterials.push({
            name: materialName,
            meshName: object.name,
            color: `#${material.color.getHexString()}`,
            metalness: material.metalness || 0,
            roughness: material.roughness || 0.5,
            opacity: material.opacity || 1,
            isEditable: materialName.startsWith('Editable')
          });
        }
      });

      // 通知父组件原始材质信息
      onOriginalMaterialsExtracted(originalMaterials);
      setMaterialsExtracted(true);
    }

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        // 若用户为该 mesh 指定了材质，则覆盖原始材质
        if (perMeshMaterials[object.name]) {
          const mat = perMeshMaterials[object.name];

          // 保存原始材质（如果还没保存过）
          if (!originalMaterialsRef.current.has(object.name) && object.material) {
            originalMaterialsRef.current.set(object.name, object.material);
          }

          const texture = mat.textureUrl ? loadTexture(mat.textureUrl) : null;

          // 如果已经有自定义材质，先清理它
          if (object.material instanceof THREE.MeshStandardMaterial &&
              createdMaterialsRef.current.includes(object.material)) {
            object.material.dispose();
            const index = createdMaterialsRef.current.indexOf(object.material);
            if (index > -1) {
              createdMaterialsRef.current.splice(index, 1);
            }
          }

          const newMat = new THREE.MeshStandardMaterial({
            color: new THREE.Color(mat.color),
            metalness: mat.metalness,
            roughness: mat.roughness,
            transparent: mat.opacity < 1,
            opacity: mat.opacity,
            map: texture
          });
          newMat.needsUpdate = true;
          object.material = newMat;

          // 跟踪新创建的材质
          createdMaterialsRef.current.push(newMat);
        } else {
          // 如果没有自定义材质，但之前有过，则恢复原始材质
          const originalMaterial = originalMaterialsRef.current.get(object.name);
          if (originalMaterial && object.material !== originalMaterial) {
            // 清理当前的自定义材质
            if (object.material instanceof THREE.MeshStandardMaterial &&
                createdMaterialsRef.current.includes(object.material)) {
              object.material.dispose();
              const index = createdMaterialsRef.current.indexOf(object.material);
              if (index > -1) {
                createdMaterialsRef.current.splice(index, 1);
              }
            }
            object.material = originalMaterial;
          }
        }
        // 否则保留模型的原始材质
      }
    });
  }, [scene, perMeshMaterials, activeMaterialName, materialsExtracted, onOriginalMaterialsExtracted]);

  return (
    <group>
      <primitive object={scene} />
    </group>
  )
}

const Loader = () => {
  return (
    <Html center>
      <Loading variant="overlay" />
    </Html>
  );
};

const RenderPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const controlsRef = useRef<OrbitControlsImpl | null>(null);
  const { modelId } = useParams<{ modelId?: string }>();
  const [logoClickCount, setLogoClickCount] = useState(0);
  const logoClickTimer = useRef<number | null>(null);
  const notify = useNotification();

  // 重置相机到默认视角
  const resetView = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset();
      controlsRef.current.target.set(0, 0, 0);
      controlsRef.current.object.position.set(0, 1, 4);
      controlsRef.current.update();
    }
  }, []);
  // 初始为空，等后台数据返回后自动赋值
  const [selectedModelId, setSelectedModelId] = useState<string>(modelId || '');
  const [searchQuery, setSearchQuery] = useState('');
  // 当前选中的系统预设材质 ID
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('会通材料');
  
  // 存储模型列表和选中的模型数据
  const [models, setModels] = useState<ModelData[]>([]);
  const [currentModel, setCurrentModel] = useState<ModelData | null>(null);
  // 存储上传的模型URL
  const [uploadedModelUrl, setUploadedModelUrl] = useState<string | null>(null);
  
  // 存储材质列表和选中的材质数据
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);
  
  // 显示加载状态和后台管理入口相关逻辑
  const showLoader = loading && (!currentModel || !currentModel.filePath);

  

  // 原始材质信息
  const [originalMaterials, setOriginalMaterials] = useState<OriginalMaterial[]>([]);
  // 当前激活的材质名称
  const [activeMaterialName, setActiveMaterialName] = useState<string | null>(null);
  // 使用ref来存储最新的激活材质名称，避免闭包陷阱
  const activeMaterialNameRef = useRef<string | null>(null);
  // 每个 mesh 对应的材质设置
  const [perMeshMaterials, setPerMeshMaterials] = useState<PerMeshMaterials>({});
  // 材质是否已经准备好
  const [materialsReady, setMaterialsReady] = useState<boolean>(false);
  // 防抖定时器

  
  // 处理模型文件上传
  const [uploadModalVisible, setUploadModalVisible] = useState(false);

  // 删除临时上传模型
  const handleRemoveUploadedModel = useCallback(() => {
    // 释放 URL
    if (uploadedModelUrl) {
      URL.revokeObjectURL(uploadedModelUrl);
    }
    setUploadedModelUrl(null);
    // 还原为后台模型数据的第一个（若存在）
    if (models.length > 0) {
      setSelectedModelId(models[0].id);
      setCurrentModel(models[0]);
    } else {
      setSelectedModelId('');
      setCurrentModel(null);
    }
    // 重置材质状态
    setActiveMaterialName(null);
    activeMaterialNameRef.current = null;
    setOriginalMaterials([]);
    setPerMeshMaterials({});
    setMaterialsReady(false);
  }, [uploadedModelUrl, models]);
  // 计算默认材质设置
  const defaultMaterialSettings = useMemo(() => {
    if (!activeMaterialName) return null;

    // 如果有系统材质被激活，使用系统材质的参数
    if (selectedPresetId) {
      const selectedMaterial = materials.find(m => m.id === selectedPresetId);
      return {
        color: selectedMaterial?.color || '#B39B9C',
        metalness: (selectedMaterial?.metalness || 50) / 100,
        roughness: (selectedMaterial?.roughness || 50) / 100,
        opacity: selectedMaterial?.glass 
          ? (100 - selectedMaterial.glass) / 100 
          : 1
      };
    }

    // 否则使用原始材质的参数
    const originalMaterial = originalMaterials.find(m => m.name === activeMaterialName);
    return {
      color: originalMaterial?.color || '#B39B9C',
      metalness: originalMaterial?.metalness || 0.5,
      roughness: originalMaterial?.roughness || 0.5,
      opacity: originalMaterial?.opacity || 1
    };
  }, [selectedPresetId, activeMaterialName, materials, originalMaterials]);

  const handleModelUpload = useCallback((file: File) => {
    // 验证文件
    const validation = validateFile(file, {
      ...FILE_VALIDATION_CONFIGS.MODEL,
      allowedExtensions: [...FILE_VALIDATION_CONFIGS.MODEL.allowedExtensions]
    });
    if (!validation.isValid) {
      notify(validation.error!, 'error');
      return;
    }

    try {
      const url = URL.createObjectURL(file);
      setUploadedModelUrl(url);
      const tempModel: ModelData = {
        id: 'uploaded-model',
        name: file.name,
        filePath: url,
        thumbnail: null,
        fileType: file.name.split('.').pop()?.toUpperCase() || null,
        size: file.size,
        createdAt: new Date().toISOString()
      };

      setCurrentModel(tempModel);
      setActiveMaterialName(null);
      activeMaterialNameRef.current = null;
      setOriginalMaterials([]);
      setPerMeshMaterials({});
      setMaterialsReady(false);
      setUploadModalVisible(false);
      notify('模型上传成功', 'success');
    } catch (error) {
      console.error('文件处理失败:', error);
      notify('文件处理失败，请重试', 'error');
    }
  }, [notify]);

  // 当从路由 state 接收到上传的文件时，立即加载
  useEffect(() => {
    const state = location.state as { uploadedFile?: File } | null;
    if (state?.uploadedFile) {
      handleModelUpload(state.uploadedFile);
    }
    // 仅在首次渲染时检查一次，避免重复处理
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 处理原始材质信息提取
  const handleOriginalMaterialsExtracted = useCallback((materials: OriginalMaterial[]) => {
    setOriginalMaterials(materials);
    setMaterialsReady(true);
    // 如果有材质，默认激活第一个可编辑材质
    const editableMaterials = materials.filter(m => m.isEditable);
    if (editableMaterials.length > 0) {
      const defaultMaterialName = editableMaterials[0].name;
      setActiveMaterialName(defaultMaterialName);
      activeMaterialNameRef.current = defaultMaterialName;
    }
  }, []);

  // 处理材质激活
  const handleMaterialActivate = useCallback((materialName: string) => {
    // 同时更新state和ref
    setActiveMaterialName(materialName);
    activeMaterialNameRef.current = materialName;

    // 当用户切换到新的可编辑材质时，清空当前选中的系统预设材质，避免高亮错位
    setSelectedPresetId(null);
  }, [setActiveMaterialName, setSelectedPresetId]);

  // 应用预设材质到指定的材质
  const applyPresetMaterialToActive = useCallback((material: MaterialData) => {
    // 使用ref中的值，确保获取到最新的激活材质名称
    const currentActiveMaterialName = activeMaterialNameRef.current;

    if (!currentActiveMaterialName || !originalMaterials.length) {
      return;
    }

    const targetMaterial = originalMaterials.find(m => m.name === currentActiveMaterialName);

    if (!targetMaterial || !targetMaterial.isEditable) {
      return;
    }

    const newSettings = {
      color: material.color,
      metalness: material.metalness / 100,
      roughness: material.roughness / 100,
      opacity: material.glass ? (100 - material.glass) / 100 : 1
    };

    // 应用到对应的mesh
    setPerMeshMaterials(prev => ({
      ...prev,
      [targetMaterial.meshName]: newSettings
    }));
  }, [originalMaterials]);

  useEffect(() => {
    const fetchData = async () => {
      // 如果有临时上传的模型，则不执行fetchData
      if (uploadedModelUrl) {
        return;
      }
      setLoading(true);
      try {
        // 获取模型数据
        const modelData = await apiService.getModels();
        setModels(modelData);

        // 预加载模型文件以提升性能
        modelData.forEach(model => {
          if (model.filePath) {
            useGLTF.preload(model.filePath);
          }
        });

        // 优先使用URL参数中的modelId，否则使用第一个模型
        // 注意：这里不再依赖selectedModel，避免循环依赖
        const targetModelId = modelId;
        if (targetModelId && modelData.length > 0) {
          const found = modelData.find(m => m.id === targetModelId);
          if (found) {
            setSelectedModelId(targetModelId);
            setCurrentModel(found);
          } else if (modelData.length > 0) {
            // 如果指定的模型不存在，使用第一个模型
            setSelectedModelId(modelData[0].id);
            setCurrentModel(modelData[0]);
          }
        } else if (modelData.length > 0 && !selectedModelId) {
          // 只有在selectedModel为空时才设置默认值
          setSelectedModelId(modelData[0].id);
          setCurrentModel(modelData[0]);
        }

        // 获取材质数据
        const materialData = await apiService.getMaterials();
        setMaterials(materialData);
      } catch (error) {
        console.error('获取数据失败:', error);
        notify('数据加载失败，请检查网络连接', 'error');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // 只依赖modelId的变化，不再依赖selectedModel
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modelId, notify]);
  
  useEffect(() => {
    if (selectedModelId) {
      const model = models.find(m => m.id === selectedModelId);
      if (model) {
        setCurrentModel(model);
      } else {
        setCurrentModel(null);
      }
    }
  }, [selectedModelId, models, setCurrentModel]);

  useEffect(() => {
    if (!selectedModelId || models.length === 0) {
      setCurrentModel(null);
      return;
    }
    const model = models.find(m => m.id === selectedModelId);
    setCurrentModel(model || null);
  }, [selectedModelId, models]);
  

  // 处理Logo点击事件
  const handleLogoClick = useCallback(() => {
    const newCount = logoClickCount + 1;
    setLogoClickCount(newCount);
    
    // 清除之前的计时器
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
    }
    
    // 设置新的计时器，2秒内未达到3次点击则重置计数
    logoClickTimer.current = window.setTimeout(() => {
      setLogoClickCount(0);
    }, 2000);
    
    // 如果达到3次点击，跳转到后台登录页面
    if (newCount === 3) {
      setLogoClickCount(0);
      if (logoClickTimer.current) {
        window.clearTimeout(logoClickTimer.current);
      }
      navigate('/admin');
    }
  }, [logoClickCount, navigate]);

  // -------------- Canvas 引用及复制 / 保存功能 --------------
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  const handleCopyImage = useCallback(async () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      // 定义 Clipboard 接口的类型声明
      interface ExtendedClipboard extends Clipboard {
        write(data: ClipboardItem[]): Promise<void>;
      }
      
      interface ClipboardItemConstructor {
        new (items: Record<string, Blob>): ClipboardItem;
      }
      
      canvas.toBlob(async (blob) => {
        if (!blob) return;
        
        try {
          // 使用类型断言处理实验性 API
          const clipboard = navigator.clipboard as unknown as ExtendedClipboard;
          const ClipboardItem = window.ClipboardItem as unknown as ClipboardItemConstructor;
          
          if (!ClipboardItem) {
            throw new Error('Clipboard API 不支持 ClipboardItem');
          }
          
          const item = new ClipboardItem({ 'image/png': blob });
          await clipboard.write([item]);
          notify('图片已复制到剪贴板', 'success');
        } catch (err) {
          console.error('复制图片失败', err);
          notify('复制图片失败，请重试', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('复制失败:', error);
      notify('复制失败，请检查浏览器权限', 'error');
    }
  }, [notify]);

  // 保存当前渲染结果到本地
  const handleSaveImage = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) {
        notify('无法获取渲染画布', 'error');
        return;
      }

      canvas.toBlob((blob) => {
        if (!blob) {
          notify('图片生成失败', 'error');
          return;
        }

        try {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `render-${new Date().getTime()}.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          notify('图片保存成功', 'success');
        } catch (error) {
          console.error('保存图片失败:', error);
          notify('保存图片失败', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('保存图片失败:', error);
      notify('保存图片失败', 'error');
    }
  }, [notify]);
  // --------------------------------------------------------------

  return (
    <div className="render-page">

      <div className="title-bar">
        <div className="title-bar__left">
          <img 
            className="logo" 
            src={LogoImage} 
            alt="RINKO" 
            style={{ height: 'var(--button-height)', cursor: 'pointer' }}
            onClick={handleLogoClick}
          />
        </div>
        <div className="user-controls">
          <SecondaryButton icon={Copy} onClick={handleCopyImage}>复制图片</SecondaryButton>
          <PrimaryButton icon={Download} onClick={handleSaveImage}>保存图片</PrimaryButton>
        </div>
      </div>

      <div className="render-container">
        <div className="render-window">
          {/* 3D渲染区域 */}
          <div className="render-area">
            <Canvas
              onCreated={({ gl }) => { canvasRef.current = gl.domElement; }}
              gl={{
                preserveDrawingBuffer: true,
                alpha: true,
                antialias: true,
                powerPreference: 'high-performance'
              }}
              shadows
              camera={{ position: [0, 1, 4], fov: 45 }}
              style={{ background: 'transparent' }}
              frameloop="demand"
              dpr={[1, 2]}>
              
              {/* 将Stage移出条件渲染 */}
              {!showLoader && currentModel && (uploadedModelUrl || currentModel.filePath) && (
                <ErrorBoundary fallback={
                  <Html center>
                    <StatusMessage
                      type="error"
                      message="模型加载失败"
                      description="请检查模型文件格式或网络连接"
                      size="small"
                    />
                  </Html>
                }>
                  <Stage environment="city" intensity={0.6} adjustCamera={false} shadows={false} preset="rembrandt" scale={1}>
                    <Model
                      modelPath={uploadedModelUrl || currentModel.filePath || ''}
                      perMeshMaterials={perMeshMaterials}
                      activeMaterialName={activeMaterialName}
                      onOriginalMaterialsExtracted={handleOriginalMaterialsExtracted}
                    />
                  </Stage>
                </ErrorBoundary>
              )}
              
              {showLoader ? (
                <Loader />
              ) : (
                <Suspense fallback={<Loader />}>
                  {!currentModel || !(uploadedModelUrl || currentModel.filePath) ? (
                    <Html center>
                      <StatusMessage
                        type="info"
                        message="未选择模型或模型数据缺失"
                        description="请从左侧选择模型或点击'上传模型'按钮"
                        size="small"
                      />
                    </Html>
                  ) : null}
                </Suspense>
              )}
              <Environment preset="city" />
              <OrbitControls 
                ref={controlsRef}
                makeDefault 
                enablePan={true} 
                enableZoom={true} 
                enableRotate={true} 
              />
            </Canvas>
          </div>
          
          <div className="button-container">
            <div 
              className="control-button"
              onClick={resetView}
              style={{ cursor: 'pointer' }}
            >
              <div className="icon-wrapper">
                <RotateCcw size={16} />
              </div>
              <span>默认视图</span>
            </div>
            <Tooltip 
              content={
                <div style={{ lineHeight: '1.8', textAlign: 'left', whiteSpace: 'nowrap' }}>
                  <div>🖱️ 左键：旋转视图</div>
                  <div>🖱️ 右键：平移视图</div>
                  <div>🖱️ 滚轮：缩放视图</div>
                </div>
              }
              position="top"
            >
              <div className="control-button">
                <div className="icon-wrapper">
                  <HelpCircle size={16} />
                </div>
                <span>操作说明</span>
              </div>
            </Tooltip>
          </div>
        </div>
        
        {/* 属性面板 */}
        <div className="property-panel">
          <div className="panel-section">
            <div className="section-header">
              <div className="icon-wrapper">
                <Box size={16} />
              </div>
              <span>模型</span>
            </div>
            {/* 如果存在临时上传模型，则展示模型卡片和删除按钮；否则展示下拉和上传按钮 */}
            <div className="dropdown-wrapper">
              {uploadedModelUrl ? (
                <div className="uploaded-model-container">
                  <div className="uploaded-model-name">
                    {currentModel?.name || '未命名模型'}
                  </div>
                  <SecondaryButton
                    icon={Trash2}
                    onClick={handleRemoveUploadedModel}
                    className="delete-model-button"
                  >
                    删除
                  </SecondaryButton>
                </div>
              ) : (
                <>
                  <DropDown
                    options={models.map(model => ({ value: model.id, label: model.name }))}
                    value={selectedModelId}
                    onChange={(value) => {
                      const id = value as string;
                      setSelectedModelId(id);
                      const found = models.find(m => m.id === id);
                      if (found) {
                        setCurrentModel(found);
                        setActiveMaterialName(null);
                        activeMaterialNameRef.current = null;
                        setOriginalMaterials([]);
                        setPerMeshMaterials({});
                      }
                    }}
                    placeholder={models.length === 0 ? '暂无模型' : ''}
                  />
                  <div className="upload-button-wrapper">
                    <SecondaryButton
                      icon={Upload}
                      fullWidth
                      onClick={() => setUploadModalVisible(true)}
                    >
                      上传模型
                    </SecondaryButton>
                  </div>
                </>
              )}
            </div>
          </div>
          
          <div className="panel-section" style={{ flex: 1 }}>
            <div className="section-header">
              <div className="icon-wrapper">
                <Paintbrush size={16} />
              </div>
              <span>材质设置</span>
            </div>
            
            <div className="editable-materials-grid">
              {!materialsReady ? (
                <EmptyState
                  title="材质加载中..."
                  size="small"
                />
              ) : originalMaterials.filter(material => material.isEditable).length > 0 ? (
                <div className="materials-grid">
                  {originalMaterials
                    .filter(material => material.isEditable)
                    .map((material, index) => {
                      // 获取当前应用的材质设置，如果有的话
                      const appliedMaterial = perMeshMaterials[material.meshName];

                      // 创建一个临时的MaterialData对象用于MaterialThumbnail
                      // 如果有应用的材质，使用应用的材质属性；否则使用原始材质属性
                      const materialData = {
                        id: `original-${material.name}`,
                        name: material.name,
                        thumbnailPath: '', // 确保字段名正确
                        color: appliedMaterial ? appliedMaterial.color : material.color,
                        metalness: appliedMaterial ? Math.round(appliedMaterial.metalness * 100) : Math.round(material.metalness * 100),
                        roughness: appliedMaterial ? Math.round(appliedMaterial.roughness * 100) : Math.round(material.roughness * 100),
                        glass: appliedMaterial ? Math.round((1 - appliedMaterial.opacity) * 100) : Math.round((1 - material.opacity) * 100),
                        createdAt: ''
                      };

                      return (
                        <MaterialThumbnail
                          key={`${material.name}-${index}`}
                          material={materialData}
                          active={material.name === activeMaterialName}
                          onClick={() => handleMaterialActivate(material.name)}
                        />
                      );
                    })}
                </div>
              ) : (
                <EmptyState
                  title="暂无可编辑材质"
                  description="当前模型没有可编辑的材质"
                  size="small"
                />
              )}
            </div>
            
            {materialsReady && (
              <>
                <TabGroup
                  className="tab-switch"
                  gap={4}
                  defaultActiveIndex={activeTab === '会通材料' ? 0 : 1}
                  onChange={(index) => setActiveTab(index === 0 ? '会通材料' : '自定义')}
                >
                  <TabItem label="会通材料" className="tab-item" />
                  <TabItem label="自定义" className="tab-item" />
                </TabGroup>

                <div className="materials-container">
                  {activeTab === '会通材料' ? (
                    <>
                      <div className="search-wrapper">
                        <SearchBox
                          placeholder="搜索"
                          value={searchQuery}
                          onChange={(value) => setSearchQuery(value)}
                          onSearch={() => {}}
                        />
                      </div>

                      <div className="materials-grid preset-materials">
                        {materials
                          .filter(material => material.name.includes(searchQuery))
                          .map((material) => (
                            <MaterialThumbnail
                              key={material.id}
                              material={material}
                              active={material.id === selectedPresetId}
                              size="preset"
                              onClick={() => {
                                // 直接应用所选材质，无需防抖
                                setSelectedPresetId(material.id);
                                applyPresetMaterialToActive(material);
                              }}
                            />
                          ))}
                      </div>
                    </>
                  ) : (
                    <>
                      {activeMaterialName && originalMaterials.find(m => m.name === activeMaterialName)?.isEditable ? (
                        <CustomMaterialPanel
                          defaultSettings={defaultMaterialSettings}
                          onChange={(settings) => {
                            if (!activeMaterialName) return;

                            const activeMaterial = originalMaterials.find(m => m.name === activeMaterialName);
                            if (!activeMaterial || !activeMaterial.isEditable) return;

                            // 用户开始自定义材质时，取消会通材料的高亮
                            setSelectedPresetId(null);

                            const newSettings = {
                              color: settings.color,
                              metalness: settings.metalness,
                              roughness: settings.roughness,
                              opacity: settings.opacity,
                              textureUrl: settings.textureUrl
                            };

                            // 应用到对应的mesh
                            setPerMeshMaterials(prev => ({
                              ...prev,
                              [activeMaterial.meshName]: newSettings
                            }));
                          }}
                        />
                      ) : (
                        <EmptyState
                          title={activeMaterialName ? '当前材质不可编辑' : '请先选择一个可编辑的材质'}
                          description={activeMaterialName ? '此材质为系统预设材质，无法修改' : '从上方材质列表中选择一个可编辑的材质'}
                          size="small"
                        />
                      )}
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {uploadModalVisible && (
        <UploadModelModal 
          visible={uploadModalVisible}
          onClose={() => setUploadModalVisible(false)}
          onUpload={handleModelUpload}
        />
      )}
    </div>
  );
};

export default RenderPage;